% systematic_buckets

% bins_assigned = {};
% for z = 0:359
%   c = find((z>=bmin|z<bmax-360) & z<=bmax);
%   bins_assigned{z+1} = c;
% end

% Fair sequential buckets - distribute across full memory space
bins_assigned = {};
memory_stride = floor(Memsize / (Nhashes * Nbuckets_per_hash));

for z = 0:359
  c = find((z>=bmin|z<bmax-360) & z<=bmax);
  % Map bucket indices to distributed memory locations
  memory_locations = mod((c-1) * memory_stride, Memsize) + 1;
  bins_assigned{z+1} = memory_locations;
end